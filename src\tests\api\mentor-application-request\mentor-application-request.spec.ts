import { expect, test } from "@playwright/test";
import { test as apiTest } from "@core/fixture/fixture-api";
import testData from "@tests-data/mentor-application-request-data.json";
import MentorApplicationRequestAPI from "src/api/mentor-application-request/mentor-application-request.api";
import LoginLogoutAPI from "src/api/login-logout/login-logout.api";
import {
    MentorApplicationRequestTestData,
    CreateApplicationRequest,
    UpdateApplicationRequest,
    GetApplicationRequestsParams,
    RequestUpdateRequest,
    RejectApplicationRequest
} from "src/data-type/mentor-application-request.type";

const data = testData as MentorApplicationRequestTestData;

test.describe('Mentor Application Request API Tests', () => {
    let mentorApplicationRequestAPI: MentorApplicationRequestAPI;
    let loginAPI: LoginLogoutAPI;

    test.beforeEach(async ({ request }) => {
        mentorApplicationRequestAPI = new MentorApplicationRequestAPI(request);
        loginAPI = new LoginLogoutAPI(request);
    });

    test.describe('Create Application Request', () => {
        test('Should create application request successfully with valid data', async ({ request }) => {
            let response: any;
            let responseBody: any;

            await test.step('Login as mentor', async () => {
                const loginResponse = await loginAPI.login(
                    data.createRequest.mentorCredentials.email,
                    data.createRequest.mentorCredentials.password
                );
                expect(loginResponse.status()).toBe(200);
            });

            await test.step('Send create application request', async () => {
                response = await mentorApplicationRequestAPI.createApplicationRequest(
                    data.createRequest.validRequest as CreateApplicationRequest
                );
                responseBody = await response.json();
            });

            await test.step('Verify successful creation', async () => {
                expect(response.status()).toBe(data.createRequest.expectedResponse.status);
                // Verify response contains expected message or success indicator
                if (responseBody.message) {
                    expect(responseBody.message).toContain('successfully');
                }
            });
        });

        test('Should handle invalid data when creating application request', async ({ request }) => {
            let response: any;

            await test.step('Login as mentor', async () => {
                const loginResponse = await loginAPI.login(
                    data.createRequest.mentorCredentials.email,
                    data.createRequest.mentorCredentials.password
                );
                expect(loginResponse.status()).toBe(200);
            });

            await test.step('Send create request with invalid data', async () => {
                response = await mentorApplicationRequestAPI.createApplicationRequest(
                    testData.errorScenarios.createRequest.invalidData as CreateApplicationRequest
                );
            });

            await test.step('Verify error response', async () => {
                expect(response.status()).toBe(testData.errorScenarios.createRequest.expectedError.status);
            });
        });
    });

    test.describe('Update Application Request', () => {
        test('Should update application request successfully', async ({ request }) => {
            let response: any;
            let responseBody: any;

            await test.step('Login as mentor', async () => {
                const loginResponse = await loginAPI.login(
                    data.updateRequest.mentorCredentials.email,
                    data.updateRequest.mentorCredentials.password
                );
                expect(loginResponse.status()).toBe(200);
            });

            await test.step('Send update application request', async () => {
                response = await mentorApplicationRequestAPI.updateApplicationRequest(
                    data.updateRequest.validUpdate as UpdateApplicationRequest
                );
                responseBody = await response.json();
            });

            await test.step('Verify successful update', async () => {
                expect(response.status()).toBe(data.updateRequest.expectedResponse.status);
                if (responseBody.message) {
                    expect(responseBody.message).toContain('successfully');
                }
            });
        });

        test('Should handle application request not found error', async ({ request }) => {
            let response: any;

            await test.step('Login as mentor', async () => {
                const loginResponse = await loginAPI.login(
                    data.updateRequest.mentorCredentials.email,
                    data.updateRequest.mentorCredentials.password
                );
                expect(loginResponse.status()).toBe(200);
            });

            await test.step('Send update request with non-existent ID', async () => {
                const invalidUpdate = {
                    ...data.updateRequest.validUpdate,
                    Id: testData.errorScenarios.updateRequest.notFound.Id
                };
                response = await mentorApplicationRequestAPI.updateApplicationRequest(
                    invalidUpdate as UpdateApplicationRequest
                );
            });

            await test.step('Verify not found error', async () => {
                expect(response.status()).toBe(testData.errorScenarios.updateRequest.notFound.expectedError.status);
            });
        });
    });

    test.describe('Get Application Requests', () => {
        test('Should get application requests with pagination', async ({ request }) => {
            let response: any;
            let responseBody: any;

            await test.step('Login as admin', async () => {
                const loginResponse = await loginAPI.login(
                    data.getRequests.adminCredentials.email,
                    data.getRequests.adminCredentials.password
                );
                expect(loginResponse.status()).toBe(200);
            });

            await test.step('Get application requests with first query params', async () => {
                response = await mentorApplicationRequestAPI.getApplicationRequests(
                    data.getRequests.queryParams[0] as GetApplicationRequestsParams
                );
                responseBody = await response.json();
            });

            await test.step('Verify successful response and data structure', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody).toBeDefined();
                
                // Verify response contains expected fields if data exists
                if (responseBody.data && Array.isArray(responseBody.data) && responseBody.data.length > 0) {
                    const firstItem = responseBody.data[0];
                    data.getRequests.expectedFields.forEach(field => {
                        expect(firstItem).toHaveProperty(field);
                    });
                }
            });
        });

        test('Should get application requests with search filter', async ({ request }) => {
            let response: any;
            let responseBody: any;

            await test.step('Login as admin', async () => {
                const loginResponse = await loginAPI.login(
                    data.getRequests.adminCredentials.email,
                    data.getRequests.adminCredentials.password
                );
                expect(loginResponse.status()).toBe(200);
            });

            await test.step('Get application requests with search filter', async () => {
                response = await mentorApplicationRequestAPI.getApplicationRequests(
                    data.getRequests.queryParams[1] as GetApplicationRequestsParams
                );
                responseBody = await response.json();
            });

            await test.step('Verify successful response', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody).toBeDefined();
            });
        });
    });

    test.describe('Get Current User Application Request', () => {
        test('Should get current user application request', async ({ request }) => {
            let response: any;
            let responseBody: any;

            await test.step('Login as mentor', async () => {
                const loginResponse = await loginAPI.login(
                    data.createRequest.mentorCredentials.email,
                    data.createRequest.mentorCredentials.password
                );
                expect(loginResponse.status()).toBe(200);
            });

            await test.step('Get current user application request', async () => {
                response = await mentorApplicationRequestAPI.getCurrentUserApplicationRequest();
                responseBody = await response.json();
            });

            await test.step('Verify successful response', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody).toBeDefined();
            });
        });
    });

    test.describe('Get Application Request by ID', () => {
        test('Should get application request by ID successfully', async ({ request }) => {
            let response: any;
            let responseBody: any;

            await test.step('Login as admin', async () => {
                const loginResponse = await loginAPI.login(
                    data.getRequestById.adminCredentials.email,
                    data.getRequestById.adminCredentials.password
                );
                expect(loginResponse.status()).toBe(200);
            });

            await test.step('Get application request by ID', async () => {
                response = await mentorApplicationRequestAPI.getApplicationRequestById(
                    data.getRequestById.requestId
                );
                responseBody = await response.json();
            });

            await test.step('Verify successful response and data structure', async () => {
                expect(response.status()).toBe(200);
                expect(responseBody).toBeDefined();

                // Verify response contains expected fields if data exists
                if (responseBody.data) {
                    data.getRequestById.expectedFields.forEach(field => {
                        expect(responseBody.data).toHaveProperty(field);
                    });
                }
            });
        });
    });

    test.describe('Request Update Application Request', () => {
        test('Should request update for application request successfully', async ({ request }) => {
            let response: any;
            let responseBody: any;

            await test.step('Login as admin', async () => {
                const loginResponse = await loginAPI.login(
                    data.requestUpdate.adminCredentials.email,
                    data.requestUpdate.adminCredentials.password
                );
                expect(loginResponse.status()).toBe(200);
            });

            await test.step('Send request update', async () => {
                response = await mentorApplicationRequestAPI.requestUpdateApplicationRequest(
                    data.requestUpdate.requestId,
                    data.requestUpdate.updateRequest as RequestUpdateRequest
                );
                responseBody = await response.json();
            });

            await test.step('Verify successful request update', async () => {
                expect(response.status()).toBe(data.requestUpdate.expectedResponse.status);
                if (responseBody.message) {
                    expect(responseBody.message).toContain('successfully');
                }
            });
        });

        test('Should handle application request not found error for request update', async ({ request }) => {
            let response: any;

            await test.step('Login as admin', async () => {
                const loginResponse = await loginAPI.login(
                    data.requestUpdate.adminCredentials.email,
                    data.requestUpdate.adminCredentials.password
                );
                expect(loginResponse.status()).toBe(200);
            });

            await test.step('Send request update with non-existent ID', async () => {
                response = await mentorApplicationRequestAPI.requestUpdateApplicationRequest(
                    testData.errorScenarios.requestUpdate.notFound.requestId,
                    data.requestUpdate.updateRequest as RequestUpdateRequest
                );
            });

            await test.step('Verify not found error', async () => {
                expect(response.status()).toBe(testData.errorScenarios.requestUpdate.notFound.expectedError.status);
            });
        });
    });

    test.describe('Approve Application Request', () => {
        test('Should approve application request successfully', async ({ request }) => {
            let response: any;
            let responseBody: any;

            await test.step('Login as admin', async () => {
                const loginResponse = await loginAPI.login(
                    data.approveRequest.adminCredentials.email,
                    data.approveRequest.adminCredentials.password
                );
                expect(loginResponse.status()).toBe(200);
            });

            await test.step('Send approve request', async () => {
                response = await mentorApplicationRequestAPI.approveApplicationRequest(
                    data.approveRequest.requestId
                );
                responseBody = await response.json();
            });

            await test.step('Verify successful approval', async () => {
                expect(response.status()).toBe(data.approveRequest.expectedResponse.status);
                if (responseBody.message) {
                    expect(responseBody.message).toContain('successfully');
                }
            });
        });

        test('Should handle application request not found error for approval', async ({ request }) => {
            let response: any;

            await test.step('Login as admin', async () => {
                const loginResponse = await loginAPI.login(
                    data.approveRequest.adminCredentials.email,
                    data.approveRequest.adminCredentials.password
                );
                expect(loginResponse.status()).toBe(200);
            });

            await test.step('Send approve request with non-existent ID', async () => {
                response = await mentorApplicationRequestAPI.approveApplicationRequest(
                    testData.errorScenarios.approveRequest.notFound.requestId
                );
            });

            await test.step('Verify not found error', async () => {
                expect(response.status()).toBe(testData.errorScenarios.approveRequest.notFound.expectedError.status);
            });
        });
    });

    test.describe('Reject Application Request', () => {
        test('Should reject application request successfully', async ({ request }) => {
            let response: any;
            let responseBody: any;

            await test.step('Login as admin', async () => {
                const loginResponse = await loginAPI.login(
                    data.rejectRequest.adminCredentials.email,
                    data.rejectRequest.adminCredentials.password
                );
                expect(loginResponse.status()).toBe(200);
            });

            await test.step('Send reject request', async () => {
                response = await mentorApplicationRequestAPI.rejectApplicationRequest(
                    data.rejectRequest.requestId,
                    data.rejectRequest.rejectRequest as RejectApplicationRequest
                );
                responseBody = await response.json();
            });

            await test.step('Verify successful rejection', async () => {
                expect(response.status()).toBe(data.rejectRequest.expectedResponse.status);
                if (responseBody.message) {
                    expect(responseBody.message).toContain('successfully');
                }
            });
        });

        test('Should handle application request not found error for rejection', async ({ request }) => {
            let response: any;

            await test.step('Login as admin', async () => {
                const loginResponse = await loginAPI.login(
                    data.rejectRequest.adminCredentials.email,
                    data.rejectRequest.adminCredentials.password
                );
                expect(loginResponse.status()).toBe(200);
            });

            await test.step('Send reject request with non-existent ID', async () => {
                response = await mentorApplicationRequestAPI.rejectApplicationRequest(
                    testData.errorScenarios.rejectRequest.notFound.requestId,
                    data.rejectRequest.rejectRequest as RejectApplicationRequest
                );
            });

            await test.step('Verify not found error', async () => {
                expect(response.status()).toBe(testData.errorScenarios.rejectRequest.notFound.expectedError.status);
            });
        });

        test('Should handle cannot reject approved request error', async ({ request }) => {
            let response: any;

            await test.step('Login as admin', async () => {
                const loginResponse = await loginAPI.login(
                    data.rejectRequest.adminCredentials.email,
                    data.rejectRequest.adminCredentials.password
                );
                expect(loginResponse.status()).toBe(200);
            });

            await test.step('Send reject request for already approved request', async () => {
                response = await mentorApplicationRequestAPI.rejectApplicationRequest(
                    testData.errorScenarios.rejectRequest.alreadyApproved.requestId,
                    data.rejectRequest.rejectRequest as RejectApplicationRequest
                );
            });

            await test.step('Verify cannot reject approved request error', async () => {
                expect(response.status()).toBe(testData.errorScenarios.rejectRequest.alreadyApproved.expectedError.status);
            });
        });
    });
});
